"use client";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuSeparator,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal } from "lucide-react";
import { useMemo, useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "react-toastify";
import { api } from "@/trpc/react";

export default function Page() {
  const {
    data: paymentAccounts,
    refetch,
    isLoading,
  } = api.paymentAccount.getAll.useQuery();
  const router = useRouter();
  const { mutate: deletePaymentAccount } =
    api.paymentAccount.delete.useMutation();

  // State for search and pagination
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);

  // Filter payment accounts based on the search term
  const filteredPaymentAccounts = useMemo(() => {
    if (!paymentAccounts) return [];
    const lowerSearch = searchTerm.toLowerCase();
    return paymentAccounts.filter(
      (account) =>
        account.bankName.toLowerCase().includes(lowerSearch) ||
        account.paymentMethods.some((method) =>
          method.toLowerCase().includes(lowerSearch),
        ),
    );
  }, [paymentAccounts, searchTerm]);

  // Calculate total pages (20 items per page)
  const totalPages = Math.ceil(filteredPaymentAccounts.length / 20);

  // Slice payment accounts list for current page
  const paginatedPaymentAccounts = useMemo(() => {
    const startIndex = (currentPage - 1) * 20;
    return filteredPaymentAccounts.slice(startIndex, startIndex + 20);
  }, [filteredPaymentAccounts, currentPage]);

  const formatPaymentMethod = (method: string) => {
    switch (method) {
      case "bankTransfer":
        return "Bank Transfer";
      case "cheque":
        return "Cheque";
      case "cash":
        return "Cash";
      case "CLIQ":
        return "CLIQ";
      case "eFAWATEERCOM":
        return "eFAWATEERCOM";
      default:
        return method;
    }
  };

  const formatPaymentMethods = (methods: string[]) => {
    return methods.map(formatPaymentMethod).join(", ");
  };

  return (
    <div className="space-y-5 p-5">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Manage Payment Accounts
            <Link href="/manage/paymentAccounts/new" className="underline">
              <Button variant={"outline"}>Create new payment account</Button>
            </Link>
          </CardTitle>
        </CardHeader>
      </Card>

      <Card>
        <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between">
          <CardTitle className="mb-2 md:mb-0">Payment Accounts</CardTitle>
          <Input
            type="text"
            placeholder="Search payment accounts"
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              setCurrentPage(1); // Reset to page 1 when searching
            }}
            className="max-w-xs"
          />
        </CardHeader>
        <CardContent>
          <Table>
            <TableCaption>
              Showing {paginatedPaymentAccounts.length} of{" "}
              {filteredPaymentAccounts.length} payment accounts
            </TableCaption>
            <TableHeader>
              <TableRow>
                <TableHead>Bank Name</TableHead>
                <TableHead>Payment Methods</TableHead>
                <TableHead>Created At</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedPaymentAccounts.map((account) => (
                <TableRow key={account.paymentAccountId}>
                  <TableCell className="font-medium">
                    {account.bankName}
                  </TableCell>
                  <TableCell className="font-medium">
                    {formatPaymentMethods(account.paymentMethods)}
                  </TableCell>
                  <TableCell className="font-medium">
                    {account.createdAt.toDateString()}
                  </TableCell>
                  <TableCell className="font-medium">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() =>
                            router.push(
                              `/manage/paymentAccounts/${account.paymentAccountId}`,
                            )
                          }
                        >
                          Edit
                        </DropdownMenuItem>
                        {/* <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => {
                            deletePaymentAccount(
                              { paymentAccountId: account.paymentAccountId },
                              {
                                onSuccess: () => {
                                  refetch();
                                  toast.success(
                                    "Payment account deleted successfully",
                                  );
                                },
                                onError: (error) => {
                                  toast.error(
                                    "Failed to delete payment account: " +
                                      error.message,
                                  );
                                },
                              },
                            );
                          }}
                        >
                          Delete
                        </DropdownMenuItem> */}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
            {(isLoading || !paymentAccounts?.length) && (
              <TableFooter>
                <TableRow>
                  <TableCell colSpan={4} className="text-center">
                    {isLoading ? "Loading..." : "No payment accounts found"}
                  </TableCell>
                </TableRow>
              </TableFooter>
            )}
          </Table>
          {/* Pagination Controls */}
          <div className="mt-4 flex items-center justify-between">
            <Button
              variant="outline"
              disabled={currentPage === 1}
              onClick={() => setCurrentPage((prev) => prev - 1)}
            >
              Previous
            </Button>
            <span>
              Page {currentPage} of {totalPages || 1}
            </span>
            <Button
              variant="outline"
              disabled={currentPage === totalPages || totalPages === 0}
              onClick={() => setCurrentPage((prev) => prev + 1)}
            >
              Next
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
