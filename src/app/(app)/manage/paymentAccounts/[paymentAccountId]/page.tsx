"use client";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { api } from "@/trpc/react";
import { PaymentMethod } from "@prisma/client";
import { Checkbox } from "@/components/ui/checkbox";

interface PaymentAccountForm {
  bankName: string;
  paymentMethods: PaymentMethod[];
}

export default function Page({
  params,
}: {
  params: { paymentAccountId: string };
}) {
  const router = useRouter();
  const isNew = params.paymentAccountId === "new";

  const { data: paymentAccount, isLoading } =
    api.paymentAccount.getOne.useQuery(
      { paymentAccountId: params.paymentAccountId },
      { enabled: !isNew },
    );

  const { mutate: createPaymentAccount, isPending: isCreating } =
    api.paymentAccount.create.useMutation();
  const { mutate: updatePaymentAccount, isPending: isUpdating } =
    api.paymentAccount.update.useMutation();

  const [paymentAccountToEdit, setPaymentAccountToEdit] =
    useState<PaymentAccountForm>({
      bankName: "",
      paymentMethods: [],
    });

  useEffect(() => {
    if (paymentAccount && !isNew) {
      setPaymentAccountToEdit({
        bankName: paymentAccount.bankName,
        paymentMethods: paymentAccount.paymentMethods,
      });
    }
  }, [paymentAccount, isNew]);

  const onClickHandler = () => {
    if (isNew) {
      createPaymentAccount(
        {
          bankName: paymentAccountToEdit.bankName,
          paymentMethods: paymentAccountToEdit.paymentMethods,
        },
        {
          onSuccess: () => {
            toast.success("Payment account created successfully");
            router.push("/manage/paymentAccounts");
          },
          onError: (error) => {
            toast.error("Failed to create payment account: " + error.message);
          },
        },
      );
    } else {
      updatePaymentAccount(
        {
          paymentAccountId: params.paymentAccountId,
          bankName: paymentAccountToEdit.bankName,
          paymentMethods: paymentAccountToEdit.paymentMethods,
        },
        {
          onSuccess: () => {
            toast.success("Payment account updated successfully");
            router.push("/manage/paymentAccounts");
          },
          onError: (error) => {
            toast.error("Failed to update payment account: " + error.message);
          },
        },
      );
    }
  };

  if (isLoading && !isNew) {
    return (
      <div className="space-y-5 p-5">
        <Card>
          <CardContent className="p-6">
            <div className="text-center">Loading...</div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-5 p-5">
      <Card>
        <CardHeader>
          <CardTitle className="flex justify-between">
            {isNew ? "Create new payment account" : "Update payment account"}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="bankName">Bank Name *</Label>
            <Input
              id="bankName"
              type="text"
              value={paymentAccountToEdit.bankName}
              onChange={(e) =>
                setPaymentAccountToEdit({
                  ...paymentAccountToEdit,
                  bankName: e.target.value,
                })
              }
              required
            />
          </div>

          <div className="space-y-3">
            <Label>Payment Methods *</Label>
            <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
              {Object.values(PaymentMethod).map((method) => {
                const isChecked =
                  paymentAccountToEdit.paymentMethods.includes(method);
                return (
                  <div key={method} className="flex items-center space-x-2">
                    <Checkbox
                      id={method}
                      checked={isChecked}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setPaymentAccountToEdit({
                            ...paymentAccountToEdit,
                            paymentMethods: [
                              ...paymentAccountToEdit.paymentMethods,
                              method,
                            ],
                          });
                        } else {
                          setPaymentAccountToEdit({
                            ...paymentAccountToEdit,
                            paymentMethods:
                              paymentAccountToEdit.paymentMethods.filter(
                                (m) => m !== method,
                              ),
                          });
                        }
                      }}
                    />
                    <Label htmlFor={method} className="text-sm font-normal">
                      {method === "bankTransfer"
                        ? "Bank Transfer"
                        : method === "cheque"
                          ? "Cheque"
                          : method === "cash"
                            ? "Cash"
                            : method === "CLIQ"
                              ? "CLIQ"
                              : method === "eFAWATEERCOM"
                                ? "eFAWATEERCOM"
                                : method}
                    </Label>
                  </div>
                );
              })}
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              onClick={() => router.push("/manage/paymentAccounts")}
            >
              Cancel
            </Button>
            <Button
              onClick={onClickHandler}
              disabled={
                isCreating ||
                isUpdating ||
                !paymentAccountToEdit.bankName ||
                paymentAccountToEdit.paymentMethods.length === 0
              }
            >
              {isCreating || isUpdating
                ? "Saving..."
                : isNew
                  ? "Create Payment Account"
                  : "Update Payment Account"}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
