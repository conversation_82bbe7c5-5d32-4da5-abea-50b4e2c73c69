import { z } from "zod";
import {
  createTRPCRouter,
  protectedProcedure,
  publicProcedure,
} from "@/server/api/trpc";
import {
  createPaymentAccountSchema,
  updatePaymentAccountSchema,
} from "@/validation/paymentAccount";

export const paymentAccountRouter = createTRPCRouter({
  getAll: publicProcedure.query(({ ctx }) => {
    return ctx.db.paymentAccount.findMany({
      orderBy: {
        createdAt: "desc",
      },
    });
  }),

  getOne: publicProcedure
    .input(z.object({ paymentAccountId: z.string() }))
    .query(async ({ ctx, input }) => {
      return ctx.db.paymentAccount.findUnique({
        where: {
          paymentAccountId: input.paymentAccountId,
        },
      });
    }),

  create: protectedProcedure
    .input(createPaymentAccountSchema)
    .mutation(async ({ ctx, input }) => {
      return ctx.db.paymentAccount.create({
        data: {
          bankName: input.bankName,
          paymentMethods: input.paymentMethods,
        },
      });
    }),

  update: protectedProcedure
    .input(updatePaymentAccountSchema)
    .mutation(async ({ ctx, input }) => {
      return ctx.db.paymentAccount.update({
        where: {
          paymentAccountId: input.paymentAccountId,
        },
        data: {
          bankName: input.bankName,
          paymentMethods: input.paymentMethods,
        },
      });
    }),

  delete: protectedProcedure
    .input(
      z.object({
        paymentAccountId: z.string().min(1),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.db.paymentAccount.delete({
        where: {
          paymentAccountId: input.paymentAccountId,
        },
      });
    }),
});
