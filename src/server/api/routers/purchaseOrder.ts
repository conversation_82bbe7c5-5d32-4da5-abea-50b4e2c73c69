import { PurchaseOrderI } from "./../../../lib/types";
import { date, promise, z } from "zod";

import {
  createTRPCRouter,
  protectedProcedure,
  publicProcedure,
} from "@/server/api/trpc";
import {
  createPOSchema,
  sendToReviewSchema,
  updatePOSchema,
} from "@/validation/purchaseOrder";
import { transporter } from "@/server/common/email.service";
import { emailTemplate } from "@/lib/utils";
import { env } from "process";
import { Notification, PaymentMethod, PaymentStatus } from "@prisma/client";

export const generateNotificationText = (
  projectName: string,
  companyName: string,
  username: string,
  type:
    | "toReview"
    | "toApprove"
    | "rejected"
    | "approve review"
    | "toPay"
    | "approved" = "toReview",
  comment: string = "",
) => {
  if (type === "toReview") {
    return `New Purchase Order for ${projectName || ""} - ${companyName || ""} created by ${username} ready for your review.`;
  } else if (type === "toApprove") {
    return `New Purchase Order for ${projectName || ""} - ${companyName || ""} created by ${username} ready for your approval.`;
  } else if (type === "rejected") {
    return (
      `Purchase Order for ${projectName || ""} - ${companyName || ""} was rejected by ${username}` +
      `${comment ? ` with the reason: ${comment}.` : "."}`
    );
  } else if (type === "approve review") {
    return `Purchase Order for ${projectName || ""} - ${companyName || ""} review was approved by ${username}`;
  } else if (type === "approved") {
    return `Purchase Order for ${projectName || ""} - ${companyName || ""} was approved by ${username}`;
  } else if (type === "toPay") {
    return `New Purchase Order for ${projectName || ""} - ${companyName || ""} is ready for payment.`;
  } else {
    return "";
  }
};

export const purchaseOrderRouter = createTRPCRouter({
  // used
  getAllPurchaseOrderData: protectedProcedure
    .input(z.object({ purchaseOrderId: z.string() }))
    .query(({ ctx, input }) => {
      const { purchaseOrderId } = input;
      return ctx.db.purchaseOrder.findUnique({
        where: {
          purchaseOrderId,
        },
        include: {
          PurchaseOrderDetails: {
            include: {
              PurchaseOrderItems: true,
              PurchaseOrderPayments: true,
              project: true,
              company: true,
            },
          },
          userApprove: true,
          userPrepare: true,
          userReview: true,
          // PurchaseOrderThreads: true,
          purchaseOrderAttachments: {
            include: {
              Attachment: true,
            },
          },
        },
      });
    }),
  // used
  getAllPurchaseOrderDataView: protectedProcedure
    .input(
      z.object({
        purchaseOrderId: z.string(),
        PurchaseOrderPaymentId: z.string(),
      }),
    )
    .query(({ ctx, input }) => {
      const { purchaseOrderId, PurchaseOrderPaymentId } = input;
      return ctx.db.purchaseOrder.findUnique({
        where: {
          purchaseOrderId,
          deletedAt: null,
        },
        include: {
          PurchaseOrderDetails: {
            include: {
              PurchaseOrderItems: true,
              PurchaseOrderPayments: {
                include: {
                  paidBy: true,
                },
              },
              project: true,
              company: true,
            },
          },
          userApprove: true,
          userPrepare: true,
          userReview: true,
          userAudit: true,
          PurchaseOrderThreads: {
            include: {
              user: {
                select: {
                  username: true,
                  email: true,
                },
              },
            },
          },
          // PurchaseOrderThreads: true,
          purchaseOrderAttachments: {
            include: {
              Attachment: true,
            },
          },
        },
      });
    }),
  // used
  reviewAndPayPayment: protectedProcedure
    .input(
      z.object({
        purchaseOrderId: z.string(),
        PurchaseOrderPaymentId: z.string(),
        currentStatus: z.enum([
          PaymentStatus.idle,
          PaymentStatus.paid,
          PaymentStatus.rejected,
          PaymentStatus.toApprove,
          PaymentStatus.toAudit,
          PaymentStatus.toPay,
          PaymentStatus.toReview,
          PaymentStatus.transferred,
        ]),
        nextStatus: z.enum([
          PaymentStatus.idle,
          PaymentStatus.paid,
          PaymentStatus.rejected,
          PaymentStatus.toApprove,
          PaymentStatus.toAudit,
          PaymentStatus.toPay,
          PaymentStatus.toReview,
          PaymentStatus.transferred,
        ]),
        comment: z.string().nullable(),
        transfer: z.string(),
        paymentAccountId: z.string().optional(),
        paymentMethod: z
          .enum([
            PaymentMethod.bankTransfer,
            PaymentMethod.cheque,
            PaymentMethod.cash,
            PaymentMethod.CLIQ,
            PaymentMethod.eFAWATEERCOM,
          ])
          .optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const {
        purchaseOrderId,
        PurchaseOrderPaymentId,
        currentStatus,
        nextStatus,
        comment,
        transfer,
        paymentAccountId,
        paymentMethod,
      } = input;
      let notifications: Notification[] = [];
      const users = await ctx.db.user.findMany({
        select: {
          email: true,
          id: true,
          username: true,
          role: true,
        },
      });
      const po = await ctx.db.purchaseOrder.findUnique({
        where: {
          purchaseOrderId: input.purchaseOrderId,
        },
        include: {
          PurchaseOrderDetails: {
            include: {
              PurchaseOrderPayments: true,
              company: true,
              project: true,
            },
          },
        },
      });
      if (!po) return;
      const payment = po.PurchaseOrderDetails?.PurchaseOrderPayments.find(
        (p) => p.PurchaseOrderPaymentId == PurchaseOrderPaymentId,
      );
      if (!payment) return;

      // Get payment account details if provided
      let paymentAccount = null;
      if (paymentAccountId) {
        paymentAccount = await ctx.db.paymentAccount.findUnique({
          where: { paymentAccountId },
        });
      }

      if (nextStatus == "rejected") {
        await ctx.db.purchaseOrderPayment.update({
          where: {
            PurchaseOrderPaymentId,
          },
          data: {
            status: nextStatus,
            auditedAt: null,
            reviewedAt: null,
            approvedAt: null,
            paidAt: null,
          },
        });
        // add to the thread
        await ctx.db.purchaseOrderThread.create({
          data: {
            type: "log",
            message:
              po.paymentType == "cash"
                ? `Purchase Order was rejected \n reason: ${comment} `
                : `Payment "${payment.description}" was rejected \n reason: ${comment}`,
            purchaseOrderId,
            id: ctx.session.user.id,
          },
        });
        // send notification to the user prepared
        const n1 = await ctx.db.notification.create({
          data: {
            text:
              po.paymentType == "cash"
                ? `Purchase Order for ${po?.PurchaseOrderDetails?.project?.projectName || ""} - ${po?.PurchaseOrderDetails?.company?.companyName || ""} was rejected by ${ctx.session.user.username}` +
                  `${comment ? ` with the reason: ${comment}.` : "."}`
                : `Payment "${payment.description}" for Purchase Order #${po.referenceNumber} was rejected by ${ctx.session.user.username}` +
                  `${comment ? ` with the reason: ${comment}.` : "."}`,
            PurchaseOrderPaymentId,
            purchaseOrderId,
            id: po?.userPrepareId,
          },
        });
        notifications.push(n1);
        // send email to the user prepared
        transporter.sendMail({
          from: env.EMAIL_USER,
          // to: users.find((u) => u.id == po?.userPrepareId)?.email || "",
          to: "<EMAIL>",

          subject:
            po.paymentType == "cash"
              ? `Purchase Order #${po.referenceNumber} was rejected`
              : `Payment "${payment.description}" rejected for Purchase Order #${po.referenceNumber}`,
          html: emailTemplate({
            toName:
              users.find((u) => u.id == po?.userPrepareId)?.username || "",
            title:
              po.paymentType == "cash"
                ? `Purchase Order #${po.referenceNumber} was rejected`
                : `Payment "${payment.description}" rejected for Purchase Order #${po.referenceNumber}`,
            url: "https://po.16thofmay.com/purchaseOrder/" + po.purchaseOrderId,
            message:
              po.paymentType == "cash"
                ? `Purchase Order #${po.referenceNumber} was rejected by ${ctx.session.user.username}` +
                  `${comment ? ` with the reason: ${comment}.` : "."}`
                : `Payment "${payment.description}" for Purchase Order #${po.referenceNumber} was rejected by ${ctx.session.user.username}` +
                  `${comment ? ` with the reason: ${comment}.` : "."}`,
          }),
        });
      } else {
        if (nextStatus === "toApprove") {
          await ctx.db.purchaseOrderPayment.update({
            where: {
              PurchaseOrderPaymentId,
            },
            data: {
              status: nextStatus,
              reviewedAt: new Date(),
              auditedAt: null,
              approvedAt: null,
              paidAt: null,
            },
          });
          // add to the thread
          await ctx.db.purchaseOrderThread.create({
            data: {
              type: "log",
              message:
                po.paymentType == "cash"
                  ? `Purchase Order #${po.referenceNumber} reviewed was approved`
                  : `Payment "${payment.description}" reviewed was approved`,
              purchaseOrderId,
              id: ctx.session.user.id,
            },
          });
          // send email & notification to the approve user so he can approve
          await ctx.db.notification.create({
            data: {
              text:
                po.paymentType == "cash"
                  ? `Purchase Order #${po.referenceNumber} waiting for your approval`
                  : `Payment "${payment.description}" for Purchase Order #${po.referenceNumber} waiting for your approval`,
              PurchaseOrderPaymentId,
              purchaseOrderId,
              id: po?.userApproveId,
            },
          });
          // send email
          transporter.sendMail({
            from: env.EMAIL_USER,

            // to: users.find((u) => u.id == po?.userApproveId)?.email || "",
            to: "<EMAIL>",
            subject:
              po.paymentType == "cash"
                ? `Purchase Order #${po.referenceNumber} waiting for your approval`
                : `Payment "${payment.description}" for Purchase Order #${po.referenceNumber} waiting for your approval`,
            html: emailTemplate({
              toName:
                users.find((u) => u.id == po?.userApproveId)?.username || "",
              title:
                po.paymentType == "cash"
                  ? `Purchase Order #${po.referenceNumber} waiting for your approval`
                  : `Payment "${payment.description}" for Purchase Order #${po.referenceNumber} waiting for your approval`,
              url:
                "https://po.16thofmay.com/purchaseOrder/process/" +
                po.purchaseOrderId +
                "_" +
                PurchaseOrderPaymentId,
              message:
                po.paymentType == "cash"
                  ? `Purchase Order #${po.referenceNumber} waiting for your approval`
                  : `Payment "${payment.description}" for Purchase Order #${po.referenceNumber} waiting for your approval`,
            }),
          });
        } else if (nextStatus === "toAudit") {
          await ctx.db.purchaseOrderPayment.update({
            where: {
              PurchaseOrderPaymentId,
            },
            data: {
              status: nextStatus,
              approvedAt: new Date(),
              auditedAt: null,
              paidAt: null,
            },
          });
          // add to the thread
          await ctx.db.purchaseOrderThread.create({
            data: {
              type: "log",
              message:
                po.paymentType == "cash"
                  ? `Purchase Order #${po.referenceNumber} was approved`
                  : `Payment "${payment.description}" was approved`,
              purchaseOrderId,
              id: ctx.session.user.id,
            },
          });
          // send email & notification to the audit user so he can audit
          const n1 = await ctx.db.notification.create({
            data: {
              text:
                po.paymentType == "cash"
                  ? `Purchase Order #${po.referenceNumber} waiting for your audit`
                  : `Payment "${payment.description}" for Purchase Order #${po.referenceNumber} waiting for your audit`,
              PurchaseOrderPaymentId,
              purchaseOrderId,
              id: po?.userAuditId,
            },
          });
          notifications.push(n1);
          // send email
          transporter.sendMail({
            from: env.EMAIL_USER,

            // to: users.find((u) => u.id == po?.userAuditId)?.email || "",
            to: "<EMAIL>",
            subject:
              po.paymentType == "cash"
                ? `Purchase Order #${po.referenceNumber} waiting for your audit`
                : `Payment "${payment.description}" for Purchase Order #${po.referenceNumber} waiting for your audit`,
            html: emailTemplate({
              toName:
                users.find((u) => u.id == po?.userAuditId)?.username || "",
              title:
                po.paymentType == "cash"
                  ? `Purchase Order #${po.referenceNumber} waiting for your audit`
                  : `Payment "${payment.description}" for Purchase Order #${po.referenceNumber} waiting for your audit`,
              url:
                "https://po.16thofmay.com/purchaseOrder/process/" +
                po.purchaseOrderId +
                "_" +
                PurchaseOrderPaymentId,
              message:
                po.paymentType == "cash"
                  ? `Purchase Order #${po.referenceNumber} waiting for your audit`
                  : `Payment "${payment.description}" for Purchase Order #${po.referenceNumber} waiting for your audit`,
            }),
          });
        } else if (nextStatus === "toPay") {
          await ctx.db.purchaseOrderPayment.update({
            where: {
              PurchaseOrderPaymentId,
            },
            data: {
              status: nextStatus,
              auditedAt: new Date(),
              paidAt: null,
            },
          });
          // add to the thread
          await ctx.db.purchaseOrderThread.create({
            data: {
              type: "log",
              message:
                po.paymentType == "cash"
                  ? `Purchase Order #${po.referenceNumber} audit was approved`
                  : `Payment "${payment.description}" audit was approved`,
              purchaseOrderId,
              id: ctx.session.user.id,
            },
          });
          // send email & notification to the accountantManager (from the user)* so he can pay
          const n1 = await ctx.db.notification.create({
            data: {
              text:
                po.paymentType == "cash"
                  ? `Purchase Order #${po.referenceNumber} waiting for your payment`
                  : `Payment "${payment.description}" for Purchase Order #${po.referenceNumber} waiting for your payment`,
              PurchaseOrderPaymentId,
              purchaseOrderId,
              id:
                users.find((u) => u.role.role === "accountantManager")?.id ||
                "",
            },
          });
          notifications.push(n1);
          // email
          transporter.sendMail({
            from: env.EMAIL_USER,

            // to:
            //   users.find((u) => u.role.role === "accountantManager")?.email ||
            //   "",
            to: "<EMAIL>",
            subject:
              po.paymentType == "cash"
                ? `Purchase Order #${po.referenceNumber} waiting for your payment`
                : `Payment "${payment.description}" for Purchase Order #${po.referenceNumber} waiting for your payment`,
            html: emailTemplate({
              toName: "accountantManager",
              title:
                po.paymentType == "cash"
                  ? `Purchase Order #${po.referenceNumber} waiting for your payment`
                  : `Payment "${payment.description}" for Purchase Order #${po.referenceNumber} waiting for your payment`,
              url:
                "https://po.16thofmay.com/purchaseOrder/process/" +
                po.purchaseOrderId +
                "_" +
                PurchaseOrderPaymentId,
              message:
                po.paymentType == "cash"
                  ? `Purchase Order #${po.referenceNumber} waiting for your payment`
                  : `Payment "${payment.description}" for Purchase Order #${po.referenceNumber} waiting for your payment`,
            }),
          });
        } else if (nextStatus === "transferred") {
          await ctx.db.purchaseOrderPayment.update({
            where: {
              PurchaseOrderPaymentId,
            },
            data: {
              status: nextStatus,
              paymentAccountId: paymentAccountId || null,
            },
          });
          // add to the thread
          await ctx.db.purchaseOrderThread.create({
            data: {
              type: "log",
              message:
                po.paymentType == "cash"
                  ? `Purchase Order #${po.referenceNumber} was assigned to "${users.find((u) => u.id === transfer)?.username}" to pay${paymentAccount ? ` using ${paymentAccount.bankName}${paymentMethod ? ` via ${paymentMethod}` : ""}` : ""}`
                  : `Payment "${payment.description}" was assigned to "${users.find((u) => u.id === transfer)?.username}" to pay${paymentAccount ? ` using ${paymentAccount.bankName}${paymentMethod ? ` via ${paymentMethod}` : ""}` : ""}`,
              purchaseOrderId,
              id: ctx.session.user.id,
            },
          });
          // send email & notification to the accountant => {transfer} that he was assigned to pay the po from the accountantManager
          const n1 = await ctx.db.notification.create({
            data: {
              text:
                po.paymentType == "cash"
                  ? `Purchase Order #${po.referenceNumber} was assigned to you to pay${paymentAccount ? ` using ${paymentAccount.bankName}${paymentMethod ? ` via ${paymentMethod}` : ""}` : ""}`
                  : `Payment "${payment.description}" for Purchase Order #${po.referenceNumber} was assigned to you to pay${paymentAccount ? ` using ${paymentAccount.bankName}${paymentMethod ? ` via ${paymentMethod}` : ""}` : ""}`,
              PurchaseOrderPaymentId,
              purchaseOrderId,
              id: transfer,
            },
          });
          notifications.push(n1);
          // email
          transporter.sendMail({
            from: env.EMAIL_USER,

            // to: users.find((u) => u.id === transfer)?.email || "",
            to: "<EMAIL>",
            subject:
              po.paymentType == "cash"
                ? `Purchase Order #${po.referenceNumber} was assigned to pay${paymentAccount ? ` using ${paymentAccount.bankName}${paymentMethod ? ` via ${paymentMethod}` : ""}` : ""}`
                : `Payment "${payment.description}" for Purchase Order #${po.referenceNumber} was assigned to pay${paymentAccount ? ` using ${paymentAccount.bankName}${paymentMethod ? ` via ${paymentMethod}` : ""}` : ""}`,
            html: emailTemplate({
              toName: users.find((u) => u.id === transfer)?.username || "",
              title:
                po.paymentType == "cash"
                  ? `Purchase Order #${po.referenceNumber} was assigned to pay${paymentAccount ? ` using ${paymentAccount.bankName}${paymentMethod ? ` via ${paymentMethod}` : ""}` : ""}`
                  : `Payment "${payment.description}" for Purchase Order #${po.referenceNumber} was assigned to pay${paymentAccount ? ` using ${paymentAccount.bankName}${paymentMethod ? ` via ${paymentMethod}` : ""}` : ""}`,
              url:
                "https://po.16thofmay.com/purchaseOrder/process/" +
                po.purchaseOrderId +
                "_" +
                PurchaseOrderPaymentId,
              message:
                po.paymentType == "cash"
                  ? `${ctx.session.user.username} assigned Purchase Order #${po.referenceNumber} to you to pay${paymentAccount ? ` using ${paymentAccount.bankName}${paymentMethod ? ` via ${paymentMethod}` : ""}` : ""}`
                  : `${ctx.session.user.username} assigned Payment "${payment.description}" for Purchase Order #${po.referenceNumber} to you to pay${paymentAccount ? ` using ${paymentAccount.bankName}${paymentMethod ? ` via ${paymentMethod}` : ""}` : ""}`,
            }),
          });
        } else if (nextStatus === "paid") {
          await ctx.db.purchaseOrderPayment.update({
            where: {
              PurchaseOrderPaymentId,
            },
            data: {
              status: nextStatus,
              paidUserId: ctx.session.user.id,
              paidAt: new Date(),
              paymentAccountId: paymentAccountId || null,
            },
          });
          // add to the thread
          await ctx.db.purchaseOrderThread.create({
            data: {
              type: "log",
              message:
                po.paymentType == "cash"
                  ? `Purchase Order #${po.referenceNumber} was paid${paymentAccount ? ` using ${paymentAccount.bankName}${paymentMethod ? ` via ${paymentMethod}` : ""}` : ""}`
                  : `Payment "${payment.description}" was paid${paymentAccount ? ` using ${paymentAccount.bankName}${paymentMethod ? ` via ${paymentMethod}` : ""}` : ""}`,
              purchaseOrderId,
              id: ctx.session.user.id,
            },
          });
          // check if all payments are paid
          const allPayments = await ctx.db.purchaseOrderPayment.findMany({
            where: {
              purchaseOrderDetailId: payment.purchaseOrderDetailId,
            },
          });
          const allPaid = allPayments.every((p) => p.status === "paid");
          if (allPaid) {
            // update purchase order isPaid to true
            await ctx.db.purchaseOrder.update({
              where: {
                purchaseOrderId: po.purchaseOrderId,
              },
              data: {
                isPaid: true,
              },
            });
          }
          // send email & notification to the user prepared that the po was paid
          const n1 = await ctx.db.notification.create({
            data: {
              text:
                po.paymentType == "cash"
                  ? `Purchase Order #${po.referenceNumber} was paid`
                  : `Payment "${payment.description}" for Purchase Order #${po.referenceNumber} was paid`,
              PurchaseOrderPaymentId,
              purchaseOrderId,
              id: po.userPrepareId,
            },
          });
          notifications.push(n1);
          // email
          transporter.sendMail({
            from: env.EMAIL_USER,

            // to: users.find((u) => u.id === po.userPrepareId)?.email || "",
            to: "<EMAIL>",
            subject:
              po.paymentType == "cash"
                ? `Purchase Order #${po.referenceNumber} was paid`
                : `Payment "${payment.description}" for Purchase Order #${po.referenceNumber} was paid`,
            html: emailTemplate({
              toName:
                users.find((u) => u.id === po.userPrepareId)?.username || "",
              title:
                po.paymentType == "cash"
                  ? `Purchase Order #${po.referenceNumber} was paid`
                  : `Payment "${payment.description}" for Purchase Order #${po.referenceNumber} was paid`,
              url:
                "https://po.16thofmay.com/purchaseOrder/process/" +
                po.purchaseOrderId +
                "_" +
                PurchaseOrderPaymentId,
              message:
                po.paymentType == "cash"
                  ? `Purchase Order #${po.referenceNumber} was paid`
                  : `Payment "${payment.description}" for Purchase Order #${po.referenceNumber} was paid`,
            }),
          });
        }
      }
      return {
        notifications,
      };
    }),

  // Used
  create: protectedProcedure
    .input(createPOSchema)
    .mutation(async ({ ctx, input }) => {
      const { po, items, payments, totalAmount, attachments } = input;
      const createdPo = await ctx.db.purchaseOrder.create({
        data: {
          // paid: po.paid,
          isPaid: false,
          isDraft: true,
          paymentType: po.paymentType,
          userApproveId: po.userApproveId,
          userReviewId: po.userReviewId,
          userAuditId: po.userAuditId,
          userPrepareId: ctx.session.user.id,

          PurchaseOrderThreads: {
            create: {
              message: `Created by ${ctx.session.user.username}`,
              type: "log",
              id: ctx.session.user.id,
            },
          },
        },
      });
      if (attachments.length) {
        const createdAttachments = await ctx.db.attachment.createManyAndReturn({
          data: attachments.map((a) => {
            return {
              url: a.url,
              key: a.key,
              name: a.name,
              type: a.type,
              // puasdrchaseOrderId: createdPo.purchaseOrderId,
            };
          }),
        });

        await ctx.db.purchaseOrderAttachment.createMany({
          data: createdAttachments.map((a) => {
            return {
              attachmentId: a.attachmentId,
              purchaseOrderId: createdPo.purchaseOrderId,
            };
          }),
        });
      }

      const podPayload = {
        paymentMethod: po.paymentMethod,
        cliq: po.paymentMethod === "CLIQ" ? po.cliq || null : null,
        description: po.description || null,
        contactName: po.contactName || null,
        contactNumber: po.contactNumber || null,
        currency: po.currency,
        iban: po.paymentMethod === "bankTransfer" ? po.iban || null : null,
        swiftCode:
          po.paymentMethod === "bankTransfer" ? po.swiftCode || null : null,
        accountName:
          po.paymentMethod === "bankTransfer" ? po.accountName || null : null,
        nameOnCheque:
          po.paymentMethod === "cheque" ? po.nameOnCheque || null : null,
        companyId: po.companyId || "",
        totalAmount: +input.totalAmount,
        identifier: po.paymentMethod === "eFAWATEERCOM" ? po.identifier : null,
        purchaseOrderId: createdPo.purchaseOrderId,
        projectId: po.projectId || "",
      };

      const createdPoD = await ctx.db.purchaseOrderDetails.create({
        data: {
          ...podPayload,
        },
      });
      console.log(createdPoD);

      const createdItems = await ctx.db.purchaseOrderItem.createManyAndReturn({
        data: items
          .filter((i) => i.priceTax > 0)
          .map((i) => {
            const { priceNoTax, priceTax, taxAmount, description } = i;
            return {
              priceNoTax,
              priceTax,
              taxAmount,
              description,
              purchaseOrderDetailId: createdPoD.purchaseOrderDetailId,
            };
          }),
      });

      await ctx.db.purchaseOrderPayment.createManyAndReturn({
        data: payments.map((p) => {
          const { amount, date, description, percentage, status } = p;
          return {
            amount,
            date,
            description,
            percentage,
            status,
            purchaseOrderDetailId: createdPoD.purchaseOrderDetailId,
          };
        }),
      });
      return { ...createdPo };
    }),
  // Used
  update: protectedProcedure
    .input(updatePOSchema)
    .mutation(async ({ ctx, input }) => {
      const { po, updatedItems, newItems, updatedPayments, newPayments } =
        input;

      const company = await ctx.db.company.findUnique({
        where: {
          companyId: po.companyId,
        },
      });

      const project = await ctx.db.project.findUnique({
        where: {
          projectId: po.projectId,
        },
      });

      const userReview = await ctx.db.user.findUnique({
        where: {
          id: po.userReviewId,
        },
      });

      const poPayments = await ctx.db.purchaseOrderPayment.findMany({
        where: {
          purchaseOrderDetailId: po.purchaseOrderDetailId,
        },
      });

      if (updatedItems.length) {
        await Promise.all(
          updatedItems.map(async (item) => {
            await ctx.db.purchaseOrderItem.update({
              data: {
                priceNoTax: item.priceNoTax,
                priceTax: item.priceTax,
                taxAmount: item.taxAmount,
                description: item.description,
              },
              where: {
                purchaseOrderItemId: item.purchaseOrderItemId,
              },
            });
          }),
        );
      }

      // create new items
      if (newItems.length) {
        const createdItems = await ctx.db.purchaseOrderItem.createManyAndReturn(
          {
            data: newItems.map((i) => {
              const { priceNoTax, priceTax, taxAmount, description } = i;
              return {
                priceNoTax,
                priceTax,
                taxAmount,
                description,
                purchaseOrderDetailId: po.purchaseOrderDetailId,
              };
            }),
          },
        );
      }
      let notifications: Notification[] = [];

      // update payments, delete old notifications, send email, create new notfications, create a thread log
      await Promise.all(
        poPayments
          .filter((p) => p.status !== "idle" && p.status !== "paid")
          .map(async (p) => {
            await ctx.db.purchaseOrderPayment.update({
              data: {
                status: "toReview",
              },
              where: {
                PurchaseOrderPaymentId: p.PurchaseOrderPaymentId,
              },
            });
            await ctx.db.notification.deleteMany({
              where: {
                PurchaseOrderPaymentId: p.PurchaseOrderPaymentId,
              },
            });
            transporter.sendMail({
              from: env.EMAIL_USER,
              // to: userReview?.email || "",
              to: "<EMAIL>",
              subject: "Purchase Order To Review",
              html: emailTemplate({
                toName: userReview?.username || "",
                title: "Purchase Order To Review",
                url:
                  "https://po.16thofmay.com/purchaseOrder/process/" +
                  po.purchaseOrderId +
                  "_" +
                  p.PurchaseOrderPaymentId,
                message:
                  po.paymentType === "cash"
                    ? `Purchase Order #${po.referenceNumber} created by ${ctx.session.user.username} ready for your review.`
                    : `${ctx.session.user.username} created a purchase order for #${po.referenceNumber} and requesting you to review the payment "${p.description || ""}" that is due on ${p.date.toDateString()}.`,
              }),
            });
            const n = await ctx.db.notification.create({
              data: {
                purchaseOrderId: po.purchaseOrderId,
                opened: false,
                seen: false,
                PurchaseOrderPaymentId: p.PurchaseOrderPaymentId,
                id: userReview?.id || "",
                text:
                  po.paymentType === "cash"
                    ? `Purchase Order for #${po.referenceNumber} created by ${ctx.session.user.username} ready for your review.`
                    : `${ctx.session.user.username} created a purchase order for #${po.referenceNumber} and requesting you to review the payment "${p.description || ""}" that is due on ${p.date.toDateString()}.`,
              },
            });
            notifications.push(n as any);
          }),
      );

      if (updatedPayments.length) {
        await Promise.all(
          updatedPayments.map(async (payment) => {
            if (payment.status !== "idle") {
              await ctx.db.purchaseOrderPayment.update({
                data: {
                  amount: payment.amount,
                  date: payment.date,
                  description: payment.description,
                  percentage: payment.percentage,
                  status: "toReview",
                  approvedAt: null,
                  reviewedAt: null,
                  auditedAt: null,
                },
                where: {
                  PurchaseOrderPaymentId: payment.PurchaseOrderPaymentId,
                },
              });
              await ctx.db.notification.deleteMany({
                where: {
                  PurchaseOrderPaymentId: payment.PurchaseOrderPaymentId,
                },
              });
              transporter.sendMail({
                from: env.EMAIL_USER,
                // to: userReview?.email || "",
                to: "<EMAIL>",
                subject: "Purchase Order To Review",
                html: emailTemplate({
                  toName: userReview?.username || "",
                  title: "Purchase Order To Review",
                  url:
                    "https://po.16thofmay.com/purchaseOrder/process/" +
                    po.purchaseOrderId +
                    "_" +
                    payment.PurchaseOrderPaymentId,
                  message:
                    po.paymentType === "cash"
                      ? `Purchase Order for ${project?.projectName || ""} - ${company?.companyName || ""} created by ${ctx.session.user.username} ready for your review.`
                      : `${ctx.session.user.username} created a purchase order for ${project?.projectName || ""} - ${company?.companyName || ""} and requesting you to review the payment "${payment.description || ""}" that is due on ${payment.date.toDateString()}.`,
                }),
              });
              await ctx.db.purchaseOrderThread.create({
                data: {
                  type: "log",
                  message: `updated ${payment.description}`,
                  purchaseOrderId: po.purchaseOrderId,
                  id: ctx.session.user.id,
                },
              });
              const n = await ctx.db.notification.create({
                data: {
                  id: po.userReviewId,
                  opened: false,
                  seen: false,
                  text:
                    po.paymentType === "cash"
                      ? `Purchase Order for ${project?.projectName || ""} - ${company?.companyName || ""} has been updated by ${ctx.session.user.username} ready for your review.`
                      : `Payment "${payment.description || ""}" in purchase order for ${project?.projectName || ""} - ${company?.companyName || ""} has been updated and it's ready for your review.`,
                  PurchaseOrderPaymentId: payment.PurchaseOrderPaymentId,
                  purchaseOrderId: po.purchaseOrderId,
                },
              });
              notifications.push(n as any);
            } else
              await ctx.db.purchaseOrderPayment.update({
                data: {
                  amount: payment.amount,
                  date: payment.date,
                  description: payment.description,
                  percentage: payment.percentage,
                  status: "idle",
                  approvedAt: null,
                  reviewedAt: null,
                  auditedAt: null,
                },
                where: {
                  PurchaseOrderPaymentId: payment.PurchaseOrderPaymentId,
                },
              });
            // delete notification for payment
          }),
        );
      }

      // just added new payments
      if (newPayments.length) {
        const createdPayments =
          await ctx.db.purchaseOrderPayment.createManyAndReturn({
            data: newPayments.map((p) => {
              const { amount, date, description, percentage, status } = p;
              return {
                amount,
                date,
                description,
                percentage,
                status: status,
                purchaseOrderDetailId: po.purchaseOrderDetailId,
              };
            }),
          });
      }

      // update the po details

      await ctx.db.purchaseOrder.update({
        where: {
          purchaseOrderId: po.purchaseOrderId,
        },
        data: {
          userReviewId: po.userReviewId,
        },
      });

      const updatedPoD = await ctx.db.purchaseOrderDetails.update({
        where: {
          purchaseOrderDetailId: po.purchaseOrderDetailId,
        },
        data: {
          cliq: po.paymentMethod === "CLIQ" ? po.cliq || null : null,
          description: po.description || null,
          contactName: po.contactName || null,
          contactNumber: po.contactNumber || null,
          currency: po.currency,
          iban: po.paymentMethod === "bankTransfer" ? po.iban || null : null,
          swiftCode:
            po.paymentMethod === "bankTransfer" ? po.swiftCode || null : null,
          accountName:
            po.paymentMethod === "bankTransfer" ? po.accountName || null : null,
          nameOnCheque:
            po.paymentMethod === "cheque" ? po.nameOnCheque || null : null,
          companyId: po.companyId || "",
          totalAmount: +input.totalAmount,

          identifier:
            po.paymentMethod === "eFAWATEERCOM" ? po.identifier : null,
          // purchaseOrderId: createdPo.purchaseOrderId,
          projectId: po.projectId || "",
        },
      });

      return { purchaseOrderId: po.purchaseOrderId, notifications };
    }),

  // Used
  delete: protectedProcedure
    .input(z.object({ purchaseOrderId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const { purchaseOrderId } = input;
      await ctx.db.purchaseOrder.update({
        data: {
          deletedAt: new Date(),
        },
        where: {
          purchaseOrderId: purchaseOrderId,
        },
      });
    }),

  // Used
  sendToReview: protectedProcedure
    .input(sendToReviewSchema)
    .mutation(
      async ({
        ctx,
        input: {
          payments,
          userReviewId,
          purchaseOrderId,
          paymentType,
          companyName,
          projectName,
        },
      }) => {
        await ctx.db.purchaseOrder.update({
          where: {
            purchaseOrderId: purchaseOrderId,
          },
          data: {
            isDraft: false,
          },
        });
        // add to the thread
        const revUser = await ctx.db.user.findUnique({
          where: {
            id: userReviewId,
          },
        });
        if (paymentType === "cash") {
          await ctx.db.purchaseOrderThread.create({
            data: {
              type: "log",
              message: `sent to purchase order to ${revUser?.username} for review`,
              purchaseOrderId: purchaseOrderId,
              id: ctx.session.user.id,
            },
          });
        } else {
          await ctx.db.purchaseOrderThread.createMany({
            data: payments.map((payment) => ({
              type: "log",
              message: `sent payment "${payment.description}" to ${revUser?.username} for review`,
              purchaseOrderId: purchaseOrderId,
              id: ctx.session.user.id,
            })),
          });
        }
        // change payment status to toReview
        await ctx.db.purchaseOrderPayment.updateMany({
          data: {
            status: "toReview",
          },
          where: {
            PurchaseOrderPaymentId: {
              in: payments.map((p) => p.PurchaseOrderPaymentId),
            },
          },
        });
        // send email
        payments.forEach((payment) => {
          transporter.sendMail({
            from: env.EMAIL_USER,
            // to: revUser?.id || "",
            to: "<EMAIL>",
            subject: "Purchase Order To Review",
            html: emailTemplate({
              toName: revUser?.username || "",
              title: "Purchase Order To Review",
              url:
                "https://po.16thofmay.com/purchaseOrder/process/" +
                purchaseOrderId +
                "_" +
                payment.PurchaseOrderPaymentId,
              message:
                paymentType === "cash"
                  ? `Purchase Order for ${projectName || ""} - ${companyName || ""} created by ${ctx.session.user.username} ready for your review.`
                  : `${ctx.session.user.username} created a purchase order for ${projectName || ""} - ${companyName || ""} and requesting you to review the payment "${payment.description || ""}" that is due on ${payment.date.toDateString()}.`,
            }),
          });
        });
        // add notifications to the toReview user
        const notifications = await ctx.db.notification.createManyAndReturn({
          data: payments.map((payment) => ({
            id: revUser?.id || "",
            opened: false,
            seen: false,
            purchaseOrderId,
            PurchaseOrderPaymentId: payment.PurchaseOrderPaymentId,
            text:
              paymentType === "cash"
                ? `Purchase Order for ${projectName || ""} - ${companyName || ""} created by ${ctx.session.user.username} ready for your review.`
                : `${ctx.session.user.username} created a purchase order for ${projectName || ""} - ${companyName || ""} and requesting you to review the payment "${payment.description || ""}" that is due on ${payment.date.toDateString()}.`,
          })),
        });

        return { notifications };
      },
    ),
  // Used
  deleteItem: protectedProcedure
    .input(z.object({ purchaseOrderItemId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.purchaseOrderItem.delete({
        where: {
          purchaseOrderItemId: input.purchaseOrderItemId,
        },
      });
    }),
  // Used
  deletePayment: protectedProcedure
    .input(
      z.object({
        PurchaseOrderPaymentId: z.string(),
        purchaseOrderId: z.string().nullable(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      if (input.purchaseOrderId) {
        await ctx.db.purchaseOrderThread.create({
          data: {
            purchaseOrderId: input.purchaseOrderId,
            message: "deleted payment",
            type: "log",
            id: ctx.session.user.id,
          },
        });
      }

      await ctx.db.notification.deleteMany({
        where: {
          PurchaseOrderPaymentId: input.PurchaseOrderPaymentId,
        },
      });
      return await ctx.db.purchaseOrderPayment.delete({
        where: {
          PurchaseOrderPaymentId: input.PurchaseOrderPaymentId,
        },
      });
    }),
});
