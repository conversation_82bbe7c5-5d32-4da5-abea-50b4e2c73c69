import { z } from "zod";
import { PaymentMethod } from "@prisma/client";

// PaymentAccount validation schemas
export const createPaymentAccountSchema = z.object({
  bankName: z.string().min(1, "Bank name is required"),
  paymentMethods: z
    .array(
      z.enum([
        PaymentMethod.bankTransfer,
        PaymentMethod.cheque,
        PaymentMethod.cash,
        PaymentMethod.CLIQ,
        PaymentMethod.eFAWATEERCOM,
      ]),
    )
    .min(1, "At least one payment method is required"),
});

export const updatePaymentAccountSchema = z.object({
  paymentAccountId: z.string().min(1, "Payment account ID is required"),
  bankName: z.string().min(1, "Bank name is required"),
  paymentMethods: z
    .array(
      z.enum([
        PaymentMethod.bankTransfer,
        PaymentMethod.cheque,
        PaymentMethod.cash,
        PaymentMethod.CLIQ,
        PaymentMethod.eFAWATEERCOM,
      ]),
    )
    .min(1, "At least one payment method is required"),
});
